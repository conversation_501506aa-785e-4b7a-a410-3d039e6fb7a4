'use client'

import { useState, useEffect } from 'react'
import { isDebugModeEnabled, setDebugMode } from '@/lib/i18n'

/**
 * Debug component to toggle i18n debug mode
 * This component allows developers to easily toggle between showing translation keys
 * and showing translated text for debugging purposes.
 */
export function I18nDebugToggle() {
  const [isDebugEnabled, setIsDebugEnabled] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    setIsDebugEnabled(isDebugModeEnabled())
  }, [])

  const handleToggle = () => {
    const newDebugState = !isDebugEnabled
    setDebugMode(newDebugState)
    setIsDebugEnabled(newDebugState)
  }

  // Don't render on server side to avoid hydration issues
  if (!isMounted) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-dark-800/90 backdrop-blur-sm border border-dark-700/50 rounded-lg p-3 shadow-lg">
        <div className="flex items-center space-x-3">
          <div className="text-sm text-dark-300">
            <div className="font-medium">i18n Debug Mode</div>
            <div className="text-xs text-dark-400">
              {isDebugEnabled ? 'Showing translation keys' : 'Showing translated text'}
            </div>
          </div>
          
          <button
            onClick={handleToggle}
            className={`
              relative inline-flex h-6 w-11 items-center rounded-full transition-colors
              ${isDebugEnabled 
                ? 'bg-primary-600 hover:bg-primary-700' 
                : 'bg-dark-600 hover:bg-dark-500'
              }
            `}
            title={`${isDebugEnabled ? 'Disable' : 'Enable'} i18n debug mode`}
          >
            <span
              className={`
                inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                ${isDebugEnabled ? 'translate-x-6' : 'translate-x-1'}
              `}
            />
          </button>
        </div>
        
        {isDebugEnabled && (
          <div className="mt-2 text-xs text-primary-400 border-t border-dark-700/50 pt-2">
            💡 Translation keys like "buttons.settings" are now visible
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * Hook to check if debug mode is currently enabled
 */
export function useI18nDebug() {
  const [isDebugEnabled, setIsDebugEnabled] = useState(false)

  useEffect(() => {
    setIsDebugEnabled(isDebugModeEnabled())
  }, [])

  return {
    isDebugEnabled,
    toggleDebugMode: () => {
      const newState = !isDebugEnabled
      setDebugMode(newState)
      setIsDebugEnabled(newState)
    }
  }
}
