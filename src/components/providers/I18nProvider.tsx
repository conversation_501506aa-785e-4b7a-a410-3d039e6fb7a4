'use client'

import { ReactNode, useState, useEffect, useCallback, useMemo } from 'react'
import { I18nContext } from '@/hooks/useI18n'
import {
  SupportedLanguage,
  TranslationValues,
  DEFAULT_LANGUAGE,
  SUPPORTED_LANGUAGES
} from '@/lib/i18n/types'
import {
  loadTranslations,
  getTranslation,
  getStoredLanguage,
  storeLanguage,
  detectBrowserLanguage,
  preloadTranslations,
  getCachedTranslations
} from '@/lib/i18n/utils'

interface I18nProviderProps {
  children: ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  // Initialize with stored language to prevent FOUC
  const [language, setLanguageState] = useState<SupportedLanguage>(() => {
    // Only access localStorage on client side
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('youtube-looper-language')
        if (stored && ['en', 'es', 'to'].includes(stored)) {
          return stored as SupportedLanguage
        }
        // Try browser language detection
        const browserLang = navigator.language.split('-')[0]
        if (['en', 'es', 'to'].includes(browserLang)) {
          return browserLang as SupportedLanguage
        }
      } catch (error) {
        console.warn('Failed to get initial language:', error)
      }
    }
    return DEFAULT_LANGUAGE
  })

  const [translations, setTranslations] = useState<Record<string, any>>({})
  const [fallbackTranslations, setFallbackTranslations] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)

  // Preload translations as early as possible
  useEffect(() => {
    // Preload the detected language translations immediately
    if (typeof window !== 'undefined') {
      preloadTranslations(language).catch(console.warn)
      // Also preload English as fallback
      if (language !== DEFAULT_LANGUAGE) {
        preloadTranslations(DEFAULT_LANGUAGE).catch(console.warn)
      }
    }
  }, []) // Run only once on mount

  // Load translations for a specific language
  const loadLanguageTranslations = useCallback(async (lang: SupportedLanguage) => {
    try {
      setIsLoading(true)

      // Try to get cached translations first for faster loading
      let newTranslations = getCachedTranslations(lang)
      if (!newTranslations) {
        newTranslations = await loadTranslations(lang)
      }
      setTranslations(newTranslations)

      // Load fallback translations (English) if current language is not English
      if (lang !== DEFAULT_LANGUAGE) {
        let fallback = getCachedTranslations(DEFAULT_LANGUAGE)
        if (!fallback) {
          fallback = await loadTranslations(DEFAULT_LANGUAGE)
        }
        setFallbackTranslations(fallback)
      } else {
        setFallbackTranslations({})
      }
    } catch (error) {
      console.error('Failed to load translations:', error)
      // On error, try to load default language
      if (lang !== DEFAULT_LANGUAGE) {
        const fallback = await loadTranslations(DEFAULT_LANGUAGE)
        setTranslations(fallback)
        setFallbackTranslations({})
      }
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // Language is already set in useState initializer to prevent FOUC
        // Just load the translations for the current language
        await loadLanguageTranslations(language)

        // Update document language attribute after hydration
        if (typeof document !== 'undefined') {
          document.documentElement.lang = language
        }

        // Initialize debug utilities
        initializeI18nDebugUtils()

        setIsInitialized(true)
      } catch (error) {
        console.error('Failed to initialize i18n:', error)
        // Fallback to default language
        if (language !== DEFAULT_LANGUAGE) {
          setLanguageState(DEFAULT_LANGUAGE)
          await loadLanguageTranslations(DEFAULT_LANGUAGE)
          if (typeof document !== 'undefined') {
            document.documentElement.lang = DEFAULT_LANGUAGE
          }
        }
        setIsInitialized(true)
      }
    }

    initializeLanguage()
  }, [language, loadLanguageTranslations]) // Include dependencies but language is stable from useState initializer

  // Change language function
  const setLanguage = useCallback(async (newLanguage: SupportedLanguage) => {
    if (newLanguage === language) return
    
    setLanguageState(newLanguage)
    storeLanguage(newLanguage)
    await loadLanguageTranslations(newLanguage)
    
    // Update document language attribute
    if (typeof document !== 'undefined') {
      document.documentElement.lang = newLanguage
    }
  }, [language, loadLanguageTranslations])

  // Translation function
  const t = useCallback((key: string, values?: TranslationValues): string => {
    return getTranslation(translations, key, values, fallbackTranslations)
  }, [translations, fallbackTranslations])

  // Context value
  const contextValue = useMemo(() => ({
    language,
    setLanguage,
    t,
    isLoading,
    availableLanguages: SUPPORTED_LANGUAGES
  }), [language, setLanguage, t, isLoading])

  // Show a very brief loading state only if translations aren't ready
  // This minimizes FOUC while avoiding hydration issues
  if (!isInitialized && isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-dark-900">
        <div className="w-6 h-6 border-2 border-primary-400/30 border-t-primary-400 rounded-full animate-spin"></div>
      </div>
    )
  }

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  )
}
