/**
 * Console utilities for i18n debugging
 * These functions are exposed globally for easy debugging from the browser console
 */

import { isDebugModeEnabled, setDebugMode } from '@/lib/i18n'

/**
 * Global debug utilities for i18n
 * These will be available in the browser console as window.tubliI18nDebug
 */
export const i18nDebugUtils = {
  /**
   * Enable i18n debug mode to show translation keys instead of translated text
   */
  enable: () => {
    setDebugMode(true)
  },

  /**
   * Disable i18n debug mode to show normal translated text
   */
  disable: () => {
    setDebugMode(false)
  },

  /**
   * Toggle i18n debug mode
   */
  toggle: () => {
    const currentState = isDebugModeEnabled()
    setDebugMode(!currentState)
  },

  /**
   * Check if debug mode is currently enabled
   */
  isEnabled: () => {
    return isDebugModeEnabled()
  },

  /**
   * Show help information about i18n debug utilities
   */
  help: () => {
    console.log(`
🔍 Tubli i18n Debug Utilities

Available commands:
• tubliI18nDebug.enable()   - Enable debug mode (show translation keys)
• tubliI18nDebug.disable()  - Disable debug mode (show translated text)
• tubliI18nDebug.toggle()   - Toggle debug mode
• tubliI18nDebug.isEnabled() - Check if debug mode is enabled
• tubliI18nDebug.help()     - Show this help message

When debug mode is enabled:
• Translation keys like "buttons.settings" will be displayed instead of "Settings"
• Keys with interpolation will show values: "time.minutesAgo (count=5)"
• This helps identify which translation keys are being used in the UI

Debug mode can also be enabled by:
• Adding ?i18n-debug=true to the URL
• Setting localStorage item 'youtube-looper-i18n-debug' to 'true'
    `)
  }
}

/**
 * Initialize global debug utilities
 * This should be called once during app initialization
 */
export function initializeI18nDebugUtils() {
  if (typeof window !== 'undefined') {
    // Make debug utilities available globally
    ;(window as any).tubliI18nDebug = i18nDebugUtils
    
    // Show a helpful message in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 i18n Debug utilities available at window.tubliI18nDebug')
      console.log('Type tubliI18nDebug.help() for more information')
    }
  }
}
