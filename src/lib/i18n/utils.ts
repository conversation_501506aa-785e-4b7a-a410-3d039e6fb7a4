// Internationalization utility functions

import { SupportedLanguage, TranslationValues, DEFAULT_LANGUAGE, LANGUAGE_STORAGE_KEY } from './types'

/**
 * Load translation file for a specific language
 */
export async function loadTranslations(language: SupportedLanguage): Promise<Record<string, any>> {
  try {
    // Dynamic import of translation files
    const translations = await import(`@/locales/${language}.json`)
    return translations.default || translations
  } catch (error) {
    console.warn(`Failed to load translations for language: ${language}`, error)

    // Fallback to default language if not already trying default
    if (language !== DEFAULT_LANGUAGE) {
      console.log(`Falling back to default language: ${DEFAULT_LANGUAGE}`)
      return loadTranslations(DEFAULT_LANGUAGE)
    }

    // If even default language fails, return empty object
    return {}
  }
}

// Translation cache to prevent FOUC
const translationCache = new Map<SupportedLanguage, Record<string, any>>()

/**
 * Preload and cache translations for immediate access
 * This helps prevent FOUC by having translations ready synchronously
 */
export async function preloadTranslations(language: SupportedLanguage): Promise<void> {
  if (translationCache.has(language)) {
    return // Already cached
  }

  try {
    const translations = await loadTranslations(language)
    translationCache.set(language, translations)

    // Also preload fallback translations if not English
    if (language !== DEFAULT_LANGUAGE && !translationCache.has(DEFAULT_LANGUAGE)) {
      const fallbackTranslations = await loadTranslations(DEFAULT_LANGUAGE)
      translationCache.set(DEFAULT_LANGUAGE, fallbackTranslations)
    }
  } catch (error) {
    console.warn(`Failed to preload translations for ${language}:`, error)
  }
}

/**
 * Get cached translations synchronously
 */
export function getCachedTranslations(language: SupportedLanguage): Record<string, any> | null {
  return translationCache.get(language) || null
}

/**
 * Get nested value from object using dot notation
 */
export function getNestedValue(obj: Record<string, any>, path: string): string | undefined {
  return path.split('.').reduce((current: any, key) => {
    return current && typeof current === 'object' ? current[key] : undefined
  }, obj)
}

/**
 * Replace placeholders in translation string with values
 */
export function interpolateString(template: string, values?: TranslationValues): string {
  if (!values) return template
  
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    const value = values[key]
    if (value !== undefined) {
      return String(value)
    }
    return match // Return original placeholder if value not found
  })
}

/**
 * Get translation for a key with optional interpolation
 */
export function getTranslation(
  translations: Record<string, any>,
  key: string,
  values?: TranslationValues,
  fallbackTranslations?: Record<string, any>
): string {
  // For debugging: return the translation key instead of translated text
  // This helps identify which translation keys are being used in the UI
  if (values && Object.keys(values).length > 0) {
    const valueStr = Object.entries(values)
      .map(([k, v]) => `${k}=${v}`)
      .join(', ')
    return `${key} (${valueStr})`
  }
  return key

  // Original translation logic (commented out for debugging)
  /*
  // Try to get translation from current language
  let translation = getNestedValue(translations, key)

  // If not found and fallback translations provided, try fallback
  if (translation === undefined && fallbackTranslations) {
    translation = getNestedValue(fallbackTranslations, key)
  }

  // If still not found, return the key itself as fallback
  if (translation === undefined) {
    console.warn(`Translation missing for key: ${key}`)
    return key
  }

  // Interpolate values if provided
  return interpolateString(translation, values)
  */
}

/**
 * Get user's preferred language from localStorage
 */
export function getStoredLanguage(): SupportedLanguage {
  if (typeof window === 'undefined') {
    return DEFAULT_LANGUAGE
  }

  try {
    const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY)
    if (stored && isValidLanguage(stored)) {
      return stored as SupportedLanguage
    }
  } catch (error) {
    console.warn('Failed to get stored language:', error)
  }

  return DEFAULT_LANGUAGE
}

/**
 * Get user's stored language preference synchronously (for initial render)
 * This function should be called during component initialization to prevent FOUC
 */
export function getStoredLanguageSync(): SupportedLanguage {
  if (typeof window === 'undefined') {
    return DEFAULT_LANGUAGE
  }

  try {
    const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY)
    if (stored && isValidLanguage(stored)) {
      return stored as SupportedLanguage
    }

    // If no stored language, try to detect from browser immediately
    const detectedLanguage = detectBrowserLanguage()
    if (detectedLanguage !== DEFAULT_LANGUAGE) {
      // Store the detected language for future use
      localStorage.setItem(LANGUAGE_STORAGE_KEY, detectedLanguage)
      return detectedLanguage
    }
  } catch (error) {
    console.warn('Failed to get stored language:', error)
  }

  return DEFAULT_LANGUAGE
}

/**
 * Store user's language preference in localStorage
 */
export function storeLanguage(language: SupportedLanguage): void {
  if (typeof window === 'undefined') {
    return
  }
  
  try {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, language)
  } catch (error) {
    console.warn('Failed to store language:', error)
  }
}

/**
 * Check if a language code is valid/supported
 */
export function isValidLanguage(language: string): boolean {
  return ['en', 'es', 'to'].includes(language)
}

/**
 * Detect user's browser language and return supported language or default
 */
export function detectBrowserLanguage(): SupportedLanguage {
  if (typeof window === 'undefined') {
    return DEFAULT_LANGUAGE
  }
  
  try {
    // Get browser language (e.g., 'en-US' -> 'en')
    const browserLang = navigator.language.split('-')[0]
    
    if (isValidLanguage(browserLang)) {
      return browserLang as SupportedLanguage
    }
  } catch (error) {
    console.warn('Failed to detect browser language:', error)
  }
  
  return DEFAULT_LANGUAGE
}

/**
 * Format relative time with localization support
 */
export function formatRelativeTime(
  date: Date | number,
  t: (key: string, values?: TranslationValues) => string
): string {
  const now = new Date()
  const targetDate = typeof date === 'number' ? new Date(date) : date
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return t('time.justNow')
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60)
  if (diffInMinutes < 60) {
    return diffInMinutes === 1 
      ? t('time.minuteAgo') 
      : t('time.minutesAgo', { count: diffInMinutes })
  }

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) {
    return diffInHours === 1 
      ? t('time.hourAgo') 
      : t('time.hoursAgo', { count: diffInHours })
  }

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) {
    return diffInDays === 1 
      ? t('time.dayAgo') 
      : t('time.daysAgo', { count: diffInDays })
  }

  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) {
    return diffInWeeks === 1 
      ? t('time.weekAgo') 
      : t('time.weeksAgo', { count: diffInWeeks })
  }

  const diffInMonths = Math.floor(diffInDays / 30)
  if (diffInMonths < 12) {
    return diffInMonths === 1 
      ? t('time.monthAgo') 
      : t('time.monthsAgo', { count: diffInMonths })
  }

  const diffInYears = Math.floor(diffInDays / 365)
  return diffInYears === 1 
    ? t('time.yearAgo') 
    : t('time.yearsAgo', { count: diffInYears })
}
