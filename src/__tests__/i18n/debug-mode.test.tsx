import { render, screen } from '@testing-library/react'
import { I18nProvider } from '@/components/providers/I18nProvider'
import { useI18n } from '@/hooks/useI18n'
import { setDebugMode, isDebugModeEnabled } from '@/lib/i18n/utils'

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
})

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    search: '',
    origin: 'http://localhost:3000',
    reload: jest.fn(),
  },
  writable: true,
})

// Test component that uses i18n
function TestComponent() {
  const { t } = useI18n()

  return (
    <div>
      <div data-testid="simple-key">{t('common.loading')}</div>
      <div data-testid="nested-key">{t('settings.title')}</div>
      <div data-testid="interpolated-key">{t('time.minutesAgo', { count: 5 })}</div>
      <div data-testid="missing-key">{t('nonexistent.key')}</div>
    </div>
  )
}

describe('i18n Debug Mode', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
    // Reset global debug flag
    ;(window as any).__TUBLI_I18N_DEBUG__ = false
  })

  afterEach(() => {
    // Clean up debug mode
    mockLocalStorage.removeItem('youtube-looper-i18n-debug')
    ;(window as any).__TUBLI_I18N_DEBUG__ = false
  })

  it('should show translated text when debug mode is disabled', async () => {
    mockLocalStorage.getItem.mockReturnValue(null)

    render(
      <I18nProvider>
        <TestComponent />
      </I18nProvider>
    )

    // Wait for translations to load
    await screen.findByTestId('simple-key')

    // Should show translated text, not keys
    expect(screen.getByTestId('simple-key')).toHaveTextContent('Loading...')
    expect(screen.getByTestId('nested-key')).toHaveTextContent('Account Settings')
  })

  it('should show translation keys when debug mode is enabled via localStorage', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'youtube-looper-i18n-debug') return 'true'
      return null
    })

    render(
      <I18nProvider>
        <TestComponent />
      </I18nProvider>
    )

    // Wait for component to render
    await screen.findByTestId('simple-key')

    // Should show translation keys, not translated text
    expect(screen.getByTestId('simple-key')).toHaveTextContent('common.loading')
    expect(screen.getByTestId('nested-key')).toHaveTextContent('settings.title')
  })

  it('should show translation keys with interpolation values in debug mode', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'youtube-looper-i18n-debug') return 'true'
      return null
    })

    render(
      <I18nProvider>
        <TestComponent />
      </I18nProvider>
    )

    // Wait for component to render
    await screen.findByTestId('interpolated-key')

    // Should show key with interpolation values
    expect(screen.getByTestId('interpolated-key')).toHaveTextContent('time.minutesAgo (count=5)')
  })

  it('should show translation keys when debug mode is enabled via URL parameter', async () => {
    // Mock URL with debug parameter
    Object.defineProperty(window, 'location', {
      value: {
        ...window.location,
        search: '?i18n-debug=true',
      },
      writable: true,
    })

    render(
      <I18nProvider>
        <TestComponent />
      </I18nProvider>
    )

    // Wait for component to render
    await screen.findByTestId('simple-key')

    // Should show translation keys
    expect(screen.getByTestId('simple-key')).toHaveTextContent('common.loading')
  })

  it('should show translation keys when debug mode is enabled via global flag', async () => {
    ;(window as any).__TUBLI_I18N_DEBUG__ = true

    render(
      <I18nProvider>
        <TestComponent />
      </I18nProvider>
    )

    // Wait for component to render
    await screen.findByTestId('simple-key')

    // Should show translation keys
    expect(screen.getByTestId('simple-key')).toHaveTextContent('common.loading')
  })

  it('should handle missing keys correctly in debug mode', async () => {
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'youtube-looper-i18n-debug') return 'true'
      return null
    })

    render(
      <I18nProvider>
        <TestComponent />
      </I18nProvider>
    )

    // Wait for component to render
    await screen.findByTestId('missing-key')

    // Should show the key itself (same behavior as normal mode for missing keys)
    expect(screen.getByTestId('missing-key')).toHaveTextContent('nonexistent.key')
  })
})
